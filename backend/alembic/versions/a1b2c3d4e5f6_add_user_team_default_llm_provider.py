"""Add user team default LLM provider table

Revision ID: a1b2c3d4e5f6
Revises: 20240322
Create Date: 2025-01-25 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f6"
down_revision = "20240322"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "user_team_default_llm_provider",
        sa.Column("user_group_id", sa.Integer(), nullable=False),
        sa.Column("llm_provider_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_group_id"],
            ["user_group.id"],
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["llm_provider_id"],
            ["llm_provider.id"],
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("user_group_id"),  # Each team gets only one default provider
    )

    op.create_index(
        "ix_user_team_default_llm_provider_user_group_id",
        "user_team_default_llm_provider",
        ["user_group_id"],
    )
    op.create_index(
        "ix_user_team_default_llm_provider_llm_provider_id",
        "user_team_default_llm_provider",
        ["llm_provider_id"],
    )

    # Populate defaults for teams that have specific LLM provider assignments
    op.execute(
        """
        INSERT INTO user_team_default_llm_provider (user_group_id, llm_provider_id)
        SELECT DISTINCT ON (lpug.user_group_id)
               lpug.user_group_id,
               lpug.llm_provider_id
        FROM llm_provider__user_group lpug
        INNER JOIN llm_provider lp ON lpug.llm_provider_id = lp.id
        ORDER BY lpug.user_group_id, lpug.llm_provider_id
        """
    )

    # Ensure ALL active teams have a default LLM provider
    # For teams without specific assignments, use the first available public provider
    op.execute(
        """
        INSERT INTO user_team_default_llm_provider (user_group_id, llm_provider_id)
        SELECT DISTINCT ON (ug.id)
               ug.id,
               lp.id
        FROM user_group ug
        CROSS JOIN llm_provider lp
        WHERE ug.is_up_to_date = true
          AND ug.is_up_for_deletion = false
          AND lp.is_public = true
          AND ug.id NOT IN (
              SELECT user_group_id
              FROM user_team_default_llm_provider
          )
        ORDER BY ug.id, lp.id
        """
    )


def downgrade() -> None:
    op.drop_index("ix_user_team_default_llm_provider_llm_provider_id")
    op.drop_index("ix_user_team_default_llm_provider_user_group_id")
    op.drop_table("user_team_default_llm_provider")
