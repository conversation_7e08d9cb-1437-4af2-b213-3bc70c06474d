import React from "react";
import { getDisplayNameForModel } from "@/lib/hooks";
import {
  checkLLMSupportsImageInput,
  destructureValue,
  structureValue,
} from "@/lib/llm/utils";
import {
  getProviderIcon,
  LLMProviderDescriptor,
} from "@/app/admin/configuration/llm/interfaces";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface LLMSelectorProps {
  userSettings?: boolean;
  llmProviders: LLMProviderDescriptor[];
  currentLlm: string | null;
  onSelect: (value: string | null) => void;
  requiresImageGeneration?: boolean;
  availableModels?: string[];
}

export const LLMSelector: React.FC<LLMSelectorProps> = ({
  userSettings,
  llmProviders,
  currentLlm,
  onSelect,
  requiresImageGeneration,
  availableModels,
}) => {
  let llmOptions = llmProviders.flatMap((provider) => {
    return (provider.display_model_names || provider.model_names)
      .map((modelName) => ({
        name: getDisplayNameForModel(modelName),
        value: structureValue(provider.name, provider.provider, modelName),
        icon: getProviderIcon(provider.provider, modelName),
        displayName: provider.name,
      }));
  });


  // For team-specific defaults, we can't easily determine the default here
  // since it depends on the user's team. The backend will resolve this.
  const defaultProvider = llmProviders.length > 0 ? llmProviders[0] : null;

  const defaultModelName = defaultProvider?.default_model_name;
  const defaultModelDisplayName = defaultModelName
    ? getDisplayNameForModel(defaultModelName)
    : null;

  const destructuredCurrentValue = currentLlm
    ? destructureValue(currentLlm)
    : null;

  const currentLlmName = destructuredCurrentValue?.modelName;
  const currentProvider = destructuredCurrentValue?.name
    ? llmProviders.find(provider => provider.name === destructuredCurrentValue.name)
    : null;

  // Check if this is a USER_DEFAULT marker
  const isUserDefault = currentLlmName === "USER_DEFAULT" || destructuredCurrentValue?.name === "USER_DEFAULT";

  return (
    <Select
      value={isUserDefault ? "default" : (currentLlm ? currentLlm : "default")}
      onValueChange={(value) => onSelect(value === "default" ? null : value)}
    >
      <SelectTrigger className="min-w-40">
        <SelectValue>
          {isUserDefault
            ? "User Default"
            : currentLlmName && currentProvider?.name
              ? `${getDisplayNameForModel(currentLlmName) || ''} (${currentProvider.name})`
              : userSettings
                ? "System Default"
                : "User Default"}
        </SelectValue>
      </SelectTrigger>
      <SelectContent className="z-[99999]">
        <SelectItem className="flex" hideCheck value="default">
          <span>{userSettings ? "System Default" : "User Default"}</span>
          {userSettings && defaultModelDisplayName && (
            <span className="my-auto font-normal ml-1">
              ({defaultModelDisplayName})
            </span>
          )}
          {!userSettings && (
            <span className="my-auto font-normal ml-1 text-xs text-muted-foreground">
              (Team Default)
            </span>
          )}
        </SelectItem>
        {llmOptions.map((option) => {
          if (
            !requiresImageGeneration ||
            checkLLMSupportsImageInput(option.name)
          ) {
            return (
              <SelectItem key={option.value} value={option.value}>
                <div className="my-1 flex items-center">
                  {option.icon && option.icon({ size: 16 })}
                  <div className="ml-2 flex flex-col">
                    <span className="text-sm font-medium">{option.name}</span>
                    <span className="text-xs text-muted-foreground">({option.displayName})</span>
                  </div>
                </div>
              </SelectItem>
            );
          }
          return null;
        })}
      </SelectContent>
    </Select>
  );
};
